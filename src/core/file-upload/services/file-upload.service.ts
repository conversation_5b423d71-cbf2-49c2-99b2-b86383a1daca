import { Injectable } from '@nestjs/common';
import { FileUploadRepository } from '../infrastructure/repositories/file-upload.repository';
import { FileUpload } from '../domain/file-upload';
import {
  FileUploadResult,
  FileUploadStatus,
} from '../domain/file-upload.types';
import { v4 as uuidv4 } from 'uuid';
import * as fs from 'fs';
import * as path from 'path';
import * as mime from 'mime-types';

@Injectable()
export class FileUploadService {
  constructor(private readonly fileUploadRepository: FileUploadRepository) {}

  async uploadFile(
    file: Express.Multer.File,
    entityType: string,
    entityId: string,
    type: string,
    createdBy: string | null,
    metadata?: Record<string, any>,
  ): Promise<FileUploadResult> {
    try {
      const fileId = uuidv4();
      const fileExtension = path.extname(file.originalname);
      const filename = `${fileId}${fileExtension}`;
      const uploadDir = path.join(process.cwd(), 'uploads');

      // Create directory if it doesn't exist
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }

      const filePath = path.join(uploadDir, filename);

      // Write file to disk
      fs.writeFileSync(filePath, file.buffer);

      const fileUpload = new FileUpload();
      fileUpload.id = fileId;
      fileUpload.filename = filename;
      fileUpload.originalFilename = file.originalname;
      fileUpload.type = type;
      fileUpload.mimeType =
        file.mimetype ||
        mime.lookup(file.originalname) ||
        'application/octet-stream';
      fileUpload.size = file.size;
      fileUpload.path = filePath;
      fileUpload.entityType = entityType;
      fileUpload.entityId = entityId;
      fileUpload.createdAt = new Date();
      fileUpload.updatedAt = new Date();
      fileUpload.createdBy = createdBy;
      fileUpload.status = FileUploadStatus.Active;
      fileUpload.metadata = metadata;

      const savedFileUpload =
        await this.fileUploadRepository.create(fileUpload);

      return {
        success: true,
        fileId: savedFileUpload.id,
        filename: savedFileUpload.filename,
        url: savedFileUpload.url,
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
        errorCode: 'FILE_UPLOAD_ERROR',
      };
    }
  }

  async uploadMultipleFiles(
    files: Express.Multer.File[],
    entityType: string,
    entityId: string,
    type: string,
    createdBy: string | null,
    metadata?: Record<string, any>,
  ): Promise<FileUploadResult[]> {
    const results: FileUploadResult[] = [];

    for (const file of files) {
      const result = await this.uploadFile(
        file,
        entityType,
        entityId,
        type,
        createdBy,
        metadata,
      );
      results.push(result);
    }

    return results;
  }

  async getFileById(id: string): Promise<FileUpload | null> {
    const uploadedFile = await this.fileUploadRepository.findById(id);
    if (uploadedFile) {
      uploadedFile.url = this.getPublicUrl(uploadedFile.filename);
    }
    return uploadedFile;
  }

  async getFilesByEntityTypeAndId(
    entityType: string,
    entityId: string,
  ): Promise<FileUpload[]> {
    const uploadedFiles = await this.fileUploadRepository.findByEntityTypeAndId(
      entityType,
      entityId,
    );

    const uploadedFilesWithURL = uploadedFiles.map((file) => {
      file.url = this.getPublicUrl(file.filename);
      return file;
    });

    return uploadedFilesWithURL;
  }

  public getPublicUrl(filename: string): string {
    const host = 'https://api.hapito.app'; //TODO: use env variable
    return `${host}/uploads/${filename}`;
  }

  async deleteFile(id: string, updatedBy: string | null): Promise<boolean> {
    try {
      const fileUpload = await this.fileUploadRepository.findById(id);

      if (!fileUpload) {
        return false;
      }

      // Soft delete in database
      await this.fileUploadRepository.softDelete(id, updatedBy);

      return true;
    } catch {
      return false;
    }
  }

  async getFileStream(
    id: string,
  ): Promise<{ stream: fs.ReadStream; mimeType: string } | null> {
    const fileUpload = await this.fileUploadRepository.findById(id);

    if (!fileUpload || fileUpload.status === FileUploadStatus.Deleted) {
      return null;
    }

    if (!fs.existsSync(fileUpload.path)) {
      return null;
    }

    const stream = fs.createReadStream(fileUpload.path);
    return {
      stream,
      mimeType: fileUpload.mimeType,
    };
  }
}
