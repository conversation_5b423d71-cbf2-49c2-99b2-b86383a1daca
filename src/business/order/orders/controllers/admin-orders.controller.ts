import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Query,
  UseGuards,
  Req,
  HttpCode,
  HttpStatus,
  Body,
  Patch,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiOkResponse,
  ApiParam,
  ApiBearerAuth,
  ApiCookieAuth,
  ApiNotFoundResponse,
  ApiBody,
  ApiCreatedResponse,
  ApiNoContentResponse,
  ApiForbiddenResponse,
  ApiConsumes,
} from '@nestjs/swagger';
import { Request } from 'express';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { OrdersService } from '../orders.service';
import { OrderDetailResponseDto } from '../dto/order-detail-response.dto';
import { FindAllOrdersResponseDto } from '../dto/find-all-orders-response.dto';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { SecureFilterService } from '@core/infrastructure/filtering/services/secure-filter.service';
import { CurrentUser } from '@core/auth/decorators/current-user.decorator';
import { JwtPayload } from '@core/auth/domain/auth.types';
import { OrderItem } from '../domain/order-item';
import { CreateOrderItemDto } from '../dto/create-order-item.dto';
import { UpdateOrderItemDto } from '../dto/update-order-item.dto';
import { OrderStatus } from '../domain/order.types';
import { OrderResponseDto } from '../dto/order-response.dto';
import { UpdateOrderDto } from '../dto/update-order.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { FileUploadService } from '../../../../core/file-upload/services/file-upload.service';

@ApiTags('Admin - Orders')
@ApiBearerAuth()
@ApiCookieAuth('session_token')
@Controller({
  path: '/admin/orders',
  version: '1',
})
@UseGuards(JwtAuthGuard)
export class AdminOrdersController {
  constructor(
    private readonly ordersService: OrdersService,
    private readonly secureFilterService: SecureFilterService,
    private readonly fileUploadService: FileUploadService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get all orders (admin)' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'List of orders with pagination info',
    type: FindAllOrdersResponseDto,
  })
  async findAll(
    @CurrentUser() userData: JwtPayload,
    @Req() request: Request,
    @Query() filter: BaseFilterDto,
  ) {
    const combinedFilter = this.secureFilterService.parseKeyOperatorValueQuery(
      request.query,
      filter,
    );

    return this.ordersService.findAll(userData.ctx.tenantId, combinedFilter);
  }

  @Get('customer/:customerId')
  @ApiOperation({ summary: 'Get orders by customer ID (admin)' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'List of orders for a specific customer with pagination info',
    type: FindAllOrdersResponseDto,
  })
  @ApiParam({
    name: 'customerId',
    description: 'Customer ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  async findByCustomerId(
    @CurrentUser() userData: JwtPayload,
    @Param('customerId') customerId: string,
    @Req() request: Request,
    @Query() filter: BaseFilterDto,
  ) {
    const combinedFilter = this.secureFilterService.parseKeyOperatorValueQuery(
      request.query,
      filter,
    );

    // Add customer filter
    // Add customer filter to where clause
    combinedFilter.where = combinedFilter.where || {};
    if (
      typeof combinedFilter.where === 'object' &&
      !Array.isArray(combinedFilter.where)
    ) {
      combinedFilter.where['customerId'] =
        combinedFilter.where['customerId'] || {};
      combinedFilter.where['customerId']['eq'] = customerId;
    }

    return this.ordersService.findAll(userData.ctx.tenantId, combinedFilter);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get order by ID (admin)' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Order details with complete history',
    type: OrderDetailResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  async findOne(
    @CurrentUser() userData: JwtPayload,
    @Param('id') id: string,
  ): Promise<OrderDetailResponseDto> {
    return this.ordersService.findOne(userData.ctx.tenantId, id);
  }

  @Get('tracking/:trackingNumber')
  @ApiOperation({ summary: 'Get order by tracking number (admin)' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    description: 'Order details with complete history',
    type: OrderDetailResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiParam({
    name: 'trackingNumber',
    description: 'Order tracking number',
    type: 'string',
    example: 'TRK-20250408-AB12C',
  })
  async findByTrackingNumber(
    @CurrentUser() userData: JwtPayload,
    @Param('trackingNumber') trackingNumber: string,
  ): Promise<OrderDetailResponseDto> {
    return this.ordersService.findByTrackingNumber(
      userData.ctx.tenantId,
      trackingNumber,
    );
  }

  @Patch(':id/status')
  @ApiOperation({ summary: 'Update order status' })
  @ApiOkResponse({
    description: 'Order status updated successfully',
    type: OrderResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiBody({
    schema: {
      type: 'object',
      required: ['status'],
      properties: {
        status: {
          type: 'string',
          enum: Object.values(OrderStatus),
          example: OrderStatus.Pending,
        },
        reason: {
          type: 'string',
          example: 'Customer requested status update',
        },
        comments: {
          type: 'string',
          example: 'Called customer to confirm',
        },
      },
    },
  })
  async updateStatus(
    @CurrentUser() userData: JwtPayload,
    @Param('id') id: string,
    @Body()
    statusUpdate: { status: OrderStatus; reason?: string; comments?: string },
  ): Promise<OrderResponseDto> {
    if (!userData.ctx.tenantId) {
      throw new Error('Tenant ID not found while updating order status');
    }

    return this.ordersService.changeStatus(
      userData.ctx.tenantId,
      id,
      userData.sub,
      statusUpdate.status,
      statusUpdate.reason,
      statusUpdate.comments,
    );
  }

  @Get(':id/items')
  @ApiOperation({ summary: 'Get all items for an order (admin)' })
  @ApiOkResponse({
    description: 'List of order items',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            example: '550e8400-e29b-41d4-a716-446655440000',
          },
          orderId: {
            type: 'string',
            example: '550e8400-e29b-41d4-a716-446655441111',
          },
          name: { type: 'string', example: 'Package' },
          description: { type: 'string', example: 'Small package' },
          quantity: { type: 'number', example: 1 },
          weight: { type: 'number', example: 2.5 },
          dimensions: {
            type: 'object',
            properties: {
              length: { type: 'number', example: 10 },
              width: { type: 'number', example: 5 },
              height: { type: 'number', example: 3 },
            },
          },
          price: { type: 'number', example: 25.99 },
          // Other order item properties
        },
      },
    },
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  async getOrderItems(
    @CurrentUser() userData: JwtPayload,
    @Param('id') id: string,
  ): Promise<OrderItem[]> {
    // Get the order details first
    const order = await this.ordersService.findOne(userData.ctx.tenantId, id);

    // Return the items from the order
    return order.items || [];
  }

  @Post(':id/items')
  @ApiOperation({ summary: 'Add an item to an order (admin)a' })
  @ApiConsumes('multipart/form-data')
  @ApiCreatedResponse({
    description: 'Item added successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: '550e8400-e29b-41d4-a716-446655440000' },
        orderId: {
          type: 'string',
          example: '550e8400-e29b-41d4-a716-446655441111',
        },

        // Other order item properties
      },
    },
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @UseInterceptors(FileInterceptor('file'))
  @ApiBody({
    // type: CreateOrderItemDto,
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'File to upload',
        },
      },
      // required: ['file'],
    },
  })
  async addOrderItem(
    @CurrentUser() userData: JwtPayload,
    @Param('id') id: string,
    @UploadedFile() file: Express.Multer.File,
    @Body() createOrderItemDto: CreateOrderItemDto,
  ): Promise<OrderItem> {
    console.log(file);

    const addedItem = await this.ordersService.addOrderItem(
      userData.ctx.tenantId,
      id,
      userData.sub,
      createOrderItemDto,
    );

    if (createOrderItemDto.imageUrl) {
      await this.fileUploadService.uploadFile(
        file,
        'order-item',
        addedItem.id,
        'image',
        userData.sub,
      );
    }

    return addedItem;
  }

  @Put(':id/items/:itemId')
  @ApiOperation({ summary: 'Update an order item (admin)' })
  @ApiOkResponse({
    description: 'Item updated successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: '550e8400-e29b-41d4-a716-446655440000' },
        orderId: {
          type: 'string',
          example: '550e8400-e29b-41d4-a716-446655441111',
        },
        // Other order item properties
      },
    },
  })
  @ApiNotFoundResponse({ description: 'Order or item not found' })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiParam({
    name: 'itemId',
    description: 'Order Item ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655442222',
  })
  @ApiBody({ type: UpdateOrderItemDto })
  async updateOrderItem(
    @CurrentUser() userData: JwtPayload,
    @Param('id') id: string,
    @Param('itemId') itemId: string,
    @Body() updateOrderItemDto: UpdateOrderItemDto,
  ): Promise<OrderItem> {
    // Add itemId to the update DTO (to ensure it's consistent with URL)
    const updateDto: UpdateOrderItemDto = {
      ...updateOrderItemDto,
      id: itemId,
    };

    return this.ordersService.updateOrderItem(
      userData.ctx.tenantId,
      id,
      userData.sub,
      updateDto,
    );
  }

  @Delete(':id/items/:itemId')
  @ApiOperation({ summary: 'Remove an item from an order (admin)' })
  @ApiNoContentResponse({
    description: 'Item has been successfully removed',
  })
  @ApiNotFoundResponse({ description: 'Order or item not found' })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiParam({
    name: 'itemId',
    description: 'Order Item ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655442222',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async removeOrderItem(
    @CurrentUser() userData: JwtPayload,
    @Param('id') id: string,
    @Param('itemId') itemId: string,
  ): Promise<void> {
    await this.ordersService.removeOrderItem(
      userData.ctx.tenantId,
      id,
      userData.sub,
      itemId,
    );
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update an order (admin)' })
  @ApiOkResponse({
    description: 'Order updated successfully',
    type: OrderResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiBody({ type: UpdateOrderDto })
  async update(
    @CurrentUser() userData: JwtPayload,
    @Param('id') id: string,
    @Body() updateOrderDto: UpdateOrderDto,
  ): Promise<OrderResponseDto> {
    return this.ordersService.update(
      userData.ctx.tenantId,
      id,
      userData.sub,
      updateOrderDto,
    );
  }
}
