import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import { AutoMap } from '@automapper/classes';
import { OrderEntity } from './order.entity';
import { PackageTemplateEntity } from '@app/business/order/package-templates/infrastructure/entities/package-template.entity';

@Entity('order_items')
export class OrderItemEntity extends EntityRelationalHelper {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @Column('uuid', { name: 'order_id' })
  orderId: string;

  @ManyToOne(() => OrderEntity, (order) => order.items, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'order_id' })
  order: OrderEntity;

  @AutoMap()
  @Column('uuid', { nullable: true, name: 'package_template_id' })
  packageTemplateId: string;

  @ManyToOne(() => PackageTemplateEntity)
  @JoinColumn({ name: 'package_template_id' })
  packageTemplate: PackageTemplateEntity;

  @AutoMap()
  @Column({ length: 50, nullable: true })
  itemType: string;

  @AutoMap()
  @Column({ default: 1 })
  quantity: number;

  @AutoMap()
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  weight: number;

  @AutoMap()
  @Column({ length: 10, default: 'kg' })
  weightUnit: string;

  @AutoMap()
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  length: number;

  @AutoMap()
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  width: number;

  @AutoMap()
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  height: number;

  @AutoMap()
  @Column({ length: 10, default: 'cm' })
  dimensionUnit: string;

  @AutoMap()
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    generatedType: 'STORED',
    asExpression: `COALESCE(length * width * height, 0)`,
  })
  volume: number;

  @AutoMap()
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  declaredValue: number;

  @AutoMap()
  @Column({ type: 'text', nullable: true })
  description: string;

  @AutoMap()
  @Column({ type: 'text', nullable: true })
  notes: string;

  @AutoMap()
  @Column({ nullable: true })
  imageUrl: string;

  @AutoMap()
  @Column({ type: 'jsonb', default: '{}' })
  metadata: Record<string, any>;

  @AutoMap()
  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date;

  @AutoMap()
  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date;
}
